# ThreadCraft - Türkçe E-Ticaret T-Shirt <PERSON>ğazası

ThreadCraft, modern ve kullanıcı dostu bir t-shirt e-ticaret platformudur. Next.js, TypeScript, MongoDB ve Tailwind CSS kullanılarak geliştirilmiştir.

## 🚀 Özellikler

- ✅ **Tam Türkçe Dil Desteği** - Tüm arayüz ve içerik Türkçe
- ✅ **Responsive Tasarım** - Mobil, tablet ve masaüstü uyumlu
- ✅ **Dark/Light Mode** - Karanlık ve aydınlık tema desteği
- ✅ **Kullanıcı Kimlik Doğrulama** - JWT tabanlı güvenli giriş sistemi
- ✅ **Ürün Yönetimi** - Kategori, filtreleme ve arama özellikleri
- ✅ **Sepet Sistemi** - Yerel depolama ile sepet yönetimi
- ✅ **Modern UI/UX** - Framer Motion animasyonları
- ✅ **SEO Optimizasyonu** - Meta etiketler ve Open Graph desteği

## 🛠️ Teknoloji Stack

- **Frontend:** Next.js 15, React 18, TypeScript
- **Styling:** Tailwind CSS
- **Database:** MongoDB + Mongoose
- **Authentication:** JWT + bcrypt
- **Icons:** Lucide React
- **State Management:** React Context API
- **Animations:** Framer Motion (hazır)

## 📦 Kurulum

1. **Projeyi klonlayın:**
```bash
git clone <repository-url>
cd threadcraft
```

2. **Bağımlılıkları yükleyin:**
```bash
npm install
```

3. **Çevre değişkenlerini ayarlayın:**
`.env.local` dosyasını düzenleyin ve MongoDB bağlantı bilgilerinizi ekleyin.

4. **Geliştirme sunucusunu başlatın:**
```bash
npm run dev
```

5. **Tarayıcınızda açın:**
[http://localhost:3000](http://localhost:3000)

## 🗄️ Veritabanı Kurulumu

1. **Örnek ürünleri yüklemek için:**
```bash
# Tarayıcınızda şu URL'yi ziyaret edin:
http://localhost:3000/api/seed
```

Bu işlem veritabanınıza örnek Türkçe t-shirt ürünleri ekleyecektir.

## 📁 Proje Yapısı

```
threadcraft/
├── src/
│   ├── app/                 # Next.js App Router sayfaları
│   │   ├── api/            # API rotaları
│   │   ├── login/          # Giriş sayfası
│   │   ├── register/       # Kayıt sayfası
│   │   ├── products/       # Ürünler sayfası
│   │   └── cart/           # Sepet sayfası
│   ├── components/         # React bileşenleri
│   ├── context/           # Global state yönetimi
│   ├── constants/         # Türkçe dil sabitleri
│   ├── lib/               # Yardımcı fonksiyonlar
│   ├── models/            # MongoDB modelleri
│   └── types/             # TypeScript tip tanımları
├── public/                # Statik dosyalar
└── ...
```

## 🔧 API Endpoints

### Kimlik Doğrulama
- `POST /api/auth/register` - Kullanıcı kaydı
- `POST /api/auth/login` - Kullanıcı girişi
- `POST /api/auth/logout` - Kullanıcı çıkışı

### Ürünler
- `GET /api/products` - Ürün listesi (filtreleme, sıralama, arama)
- `POST /api/products` - Yeni ürün ekleme (admin)

### Veritabanı
- `POST /api/seed` - Örnek veri yükleme

## 🎨 Özelleştirme

### Dil Sabitleri
Tüm Türkçe metinler `src/constants/tr.ts` dosyasında tanımlanmıştır. Yeni metinler eklemek veya mevcut metinleri değiştirmek için bu dosyayı düzenleyin.

### Tema Renkleri
Tailwind CSS konfigürasyonu ile renk paletini özelleştirebilirsiniz.

### Ürün Kategorileri
Yeni kategoriler eklemek için `src/models/Product.ts` ve `src/constants/tr.ts` dosyalarını güncelleyin.

## 🚀 Deployment

### Vercel (Önerilen)
1. GitHub'a push edin
2. Vercel'e bağlayın
3. Çevre değişkenlerini ayarlayın
4. Deploy edin

### Diğer Platformlar
- Railway
- Render
- Heroku
- DigitalOcean

## 🧪 Test

```bash
# Unit testler (gelecekte eklenecek)
npm run test

# E2E testler (gelecekte eklenecek)
npm run test:e2e
```

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add some amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📞 İletişim

Sorularınız için GitHub Issues kullanabilirsiniz.

---

**ThreadCraft** - Kaliteli T-shirt'ler için modern e-ticaret çözümü 🎽
