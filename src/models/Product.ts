import mongoose, { Document, Schema } from 'mongoose';

export interface IProduct extends Document {
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  sizes: string[];
  colors: string[];
  stock: { [key: string]: number }; // size-color combination stock
  isActive: boolean;
  featured: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

const ProductSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Ürün adı zorunludur'],
      trim: true,
      maxlength: [100, 'Ürün adı en fazla 100 karakter olabilir'],
    },
    description: {
      type: String,
      required: [true, 'Ürün açıklaması zorunludur'],
      maxlength: [1000, 'Açıklama en fazla 1000 karakter olabilir'],
    },
    price: {
      type: Number,
      required: [true, 'Fiyat zorunludur'],
      min: [0, 'Fiyat negatif olamaz'],
    },
    images: {
      type: [String],
      required: [true, 'En az bir ürün resmi gereklidir'],
      validate: {
        validator: function(v: string[]) {
          return v && v.length > 0;
        },
        message: 'En az bir ürün resmi gereklidir',
      },
    },
    category: {
      type: String,
      required: [true, 'Kategori zorunludur'],
      enum: ['men', 'women', 'unisex', 'kids'],
    },
    sizes: {
      type: [String],
      required: [true, 'En az bir beden seçilmelidir'],
      enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    },
    colors: {
      type: [String],
      required: [true, 'En az bir renk seçilmelidir'],
    },
    stock: {
      type: Map,
      of: Number,
      default: {},
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    featured: {
      type: Boolean,
      default: false,
    },
    tags: {
      type: [String],
      default: [],
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance
ProductSchema.index({ category: 1 });
ProductSchema.index({ isActive: 1 });
ProductSchema.index({ featured: 1 });
ProductSchema.index({ name: 'text', description: 'text' });

export default mongoose.models.Product || mongoose.model<IProduct>('Product', ProductSchema);
