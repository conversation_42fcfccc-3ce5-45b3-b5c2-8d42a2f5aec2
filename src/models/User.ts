import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: 'user' | 'admin';
  isEmailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema = new Schema(
  {
    firstName: {
      type: String,
      required: [true, 'Ad alanı zorunludur'],
      trim: true,
      maxlength: [50, 'Ad en fazla 50 karakter olabilir'],
    },
    lastName: {
      type: String,
      required: [true, 'Soyad alanı zorunludur'],
      trim: true,
      maxlength: [50, 'Soyad en fazla 50 karakter olabilir'],
    },
    email: {
      type: String,
      required: [true, 'E-posta alanı zorunludur'],
      unique: true,
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        '<PERSON><PERSON><PERSON><PERSON><PERSON> bir e-posta adresi girin',
      ],
    },
    password: {
      type: String,
      required: [true, '<PERSON><PERSON><PERSON> alanı zorunludur'],
      minlength: [6, '<PERSON><PERSON><PERSON> en az 6 karakter olmalıdır'],
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user',
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
UserSchema.index({ email: 1 });

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
