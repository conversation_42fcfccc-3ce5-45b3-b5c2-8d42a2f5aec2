'use client';

import React from 'react';
import Link from 'next/link';
import { useCart } from '@/context/AppContext';
import { TR } from '@/constants/tr';
import { Minus, Plus, Trash2, ShoppingBag, ArrowRight } from 'lucide-react';

export default function CartPage() {
  const { cart, removeFromCart, updateCartItem, getCartTotal, getCartItemsCount } = useCart();

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
    } else {
      updateCartItem(itemId, newQuantity);
    }
  };

  const shippingCost = getCartTotal() > 200 ? 0 : 15;
  const taxRate = 0.18; // 18% KDV
  const subtotal = getCartTotal();
  const taxAmount = subtotal * taxRate;
  const total = subtotal + shippingCost + taxAmount;

  if (cart.length === 0) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-md mx-auto text-center">
            <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
              <ShoppingBag className="h-12 w-12 text-gray-400" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {TR.cart.empty}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              {TR.cart.emptyDescription}
            </p>
            <Link
              href="/products"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              {TR.cart.continueShopping}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {TR.cart.title} ({getCartItemsCount()} ürün)
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {cart.map((item) => (
                  <div key={`${item.id}-${item.size}-${item.color}`} className="p-6">
                    <div className="flex items-center space-x-4">
                      {/* Product Image */}
                      <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                          {item.name}
                        </h3>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Beden: {item.size}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Renk: {TR.colors[item.color as keyof typeof TR.colors] || item.color}
                          </span>
                        </div>
                        <p className="text-lg font-bold text-blue-600 dark:text-blue-400 mt-2">
                          {item.price}{TR.common.currency}
                        </p>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Minus className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        </button>
                        <span className="w-8 text-center font-semibold text-gray-900 dark:text-white">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Plus className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        </button>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeFromCart(`${item.id}-${item.size}-${item.color}`)}
                        className="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Continue Shopping */}
            <div className="mt-6">
              <Link
                href="/products"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 font-medium"
              >
                ← {TR.cart.continueShopping}
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-4">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                Sipariş Özeti
              </h2>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{TR.cart.subtotal}</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {subtotal.toFixed(2)}{TR.common.currency}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{TR.cart.shipping}</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {shippingCost === 0 ? (
                      <span className="text-green-600 dark:text-green-400">{TR.cart.freeShipping}</span>
                    ) : (
                      `${shippingCost.toFixed(2)}${TR.common.currency}`
                    )}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{TR.cart.tax} (%18)</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {taxAmount.toFixed(2)}{TR.common.currency}
                  </span>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between">
                    <span className="text-lg font-bold text-gray-900 dark:text-white">{TR.cart.total}</span>
                    <span className="text-lg font-bold text-gray-900 dark:text-white">
                      {total.toFixed(2)}{TR.common.currency}
                    </span>
                  </div>
                </div>

                {shippingCost > 0 && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                    <p className="text-sm text-blue-600 dark:text-blue-400">
                      200{TR.common.currency} ve üzeri alışverişlerde kargo ücretsiz!
                    </p>
                  </div>
                )}

                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                  {TR.cart.checkout}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </button>

                <div className="text-center">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Güvenli ödeme ile korumalı alışveriş
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
