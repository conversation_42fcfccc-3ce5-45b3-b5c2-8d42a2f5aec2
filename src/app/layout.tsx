import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AppProvider } from "@/context/AppContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ThreadCraft - Kaliteli T-Shirt Koleksiyonu",
  description: "ThreadCraft ile tarzınızı yansıtan kaliteli t-shirt'leri keşfedin. Geniş renk ve beden seçenekleri ile hızlı teslimat.",
  keywords: "t-shirt, tişört, giyim, moda, kaliteli, ThreadCraft",
  authors: [{ name: "ThreadCraft" }],
  openGraph: {
    title: "ThreadCraft - Kaliteli T-Shirt Koleksiyonu",
    description: "ThreadCraft ile tarzınızı yansıtan kaliteli t-shirt'leri keşfedin.",
    type: "website",
    locale: "tr_TR",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <AppProvider>
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </AppProvider>
      </body>
    </html>
  );
}
