import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/models/Product';

const sampleProducts = [
  {
    name: '<PERSON><PERSON><PERSON>az T-Shirt',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kullanım i<PERSON><PERSON>, %100 pamuk klasik beyaz t-shirt. Yumuşak dokusu ve rahat kesimiyle her ortamda rahatlıkla giyebilirsiniz.',
    price: 89,
    images: [
      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500',
      'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500'
    ],
    category: 'unisex',
    sizes: ['S', 'M', 'L', 'XL'],
    colors: ['white'],
    stock: new Map([
      ['S-white', 25],
      ['M-white', 30],
      ['L-white', 35],
      ['XL-white', 20]
    ]),
    featured: true,
    tags: ['klasik', 'günlük', 'pamuk']
  },
  {
    name: 'Vintage Siyah T-Shirt',
    description: 'Retro tarzı sevenler için özel tasarım vintage siyah t-shirt. Kaliteli kumaş ve şık görünümüyle stilinizi tamamlar.',
    price: 99,
    images: [
      'https://images.unsplash.com/photo-1583743814966-8936f37f4ec2?w=500',
      'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=500'
    ],
    category: 'men',
    sizes: ['S', 'M', 'L', 'XL', 'XXL'],
    colors: ['black'],
    stock: new Map([
      ['S-black', 15],
      ['M-black', 25],
      ['L-black', 30],
      ['XL-black', 20],
      ['XXL-black', 10]
    ]),
    featured: true,
    tags: ['vintage', 'siyah', 'erkek']
  },
  {
    name: 'Kadın Pembe T-Shirt',
    description: 'Kadınlar için özel tasarlanmış pembe t-shirt. Vücut hatlarını saran kesimi ve yumuşak kumaşıyla günlük şıklığınızı artırır.',
    price: 79,
    images: [
      'https://images.unsplash.com/photo-1571945153237-4929e783af4a?w=500',
      'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500'
    ],
    category: 'women',
    sizes: ['XS', 'S', 'M', 'L', 'XL'],
    colors: ['pink'],
    stock: new Map([
      ['XS-pink', 20],
      ['S-pink', 25],
      ['M-pink', 30],
      ['L-pink', 25],
      ['XL-pink', 15]
    ]),
    featured: false,
    tags: ['kadın', 'pembe', 'günlük']
  },
  {
    name: 'Spor Mavi T-Shirt',
    description: 'Spor aktiviteleri için ideal mavi t-shirt. Nefes alabilir kumaş teknolojisi ile ter emici özelliğe sahiptir.',
    price: 119,
    images: [
      'https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500',
      'https://images.unsplash.com/photo-1581655353564-df123a1eb820?w=500'
    ],
    category: 'unisex',
    sizes: ['S', 'M', 'L', 'XL'],
    colors: ['blue'],
    stock: new Map([
      ['S-blue', 20],
      ['M-blue', 25],
      ['L-blue', 30],
      ['XL-blue', 20]
    ]),
    featured: true,
    tags: ['spor', 'mavi', 'aktif']
  },
  {
    name: 'Çocuk Renkli T-Shirt',
    description: 'Çocuklar için eğlenceli ve renkli t-shirt. Yumuşak kumaş ve canlı renklerle çocuğunuzun favorisi olacak.',
    price: 59,
    images: [
      'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500',
      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500'
    ],
    category: 'kids',
    sizes: ['XS', 'S', 'M'],
    colors: ['red', 'yellow', 'green'],
    stock: new Map([
      ['XS-red', 15],
      ['S-red', 20],
      ['M-red', 15],
      ['XS-yellow', 15],
      ['S-yellow', 20],
      ['M-yellow', 15],
      ['XS-green', 15],
      ['S-green', 20],
      ['M-green', 15]
    ]),
    featured: false,
    tags: ['çocuk', 'renkli', 'eğlenceli']
  },
  {
    name: 'Grafik Baskılı T-Shirt',
    description: 'Özgün grafik tasarımı ile dikkat çeken baskılı t-shirt. Sanatsal dokunuş arayanlar için mükemmel seçim.',
    price: 109,
    images: [
      'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=500',
      'https://images.unsplash.com/photo-1583743814966-8936f37f4ec2?w=500'
    ],
    category: 'unisex',
    sizes: ['S', 'M', 'L', 'XL'],
    colors: ['black', 'white'],
    stock: new Map([
      ['S-black', 15],
      ['M-black', 20],
      ['L-black', 25],
      ['XL-black', 15],
      ['S-white', 15],
      ['M-white', 20],
      ['L-white', 25],
      ['XL-white', 15]
    ]),
    featured: true,
    tags: ['grafik', 'baskılı', 'sanat']
  },
  {
    name: 'Premium Gri T-Shirt',
    description: 'Premium kalite gri t-shirt. Üstün kumaş kalitesi ve mükemmel işçilik ile uzun yıllar kullanabilirsiniz.',
    price: 139,
    images: [
      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500',
      'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500'
    ],
    category: 'men',
    sizes: ['M', 'L', 'XL', 'XXL'],
    colors: ['gray'],
    stock: new Map([
      ['M-gray', 20],
      ['L-gray', 25],
      ['XL-gray', 20],
      ['XXL-gray', 15]
    ]),
    featured: false,
    tags: ['premium', 'gri', 'kaliteli']
  },
  {
    name: 'Kadın V Yaka T-Shirt',
    description: 'Şık V yaka kesimi ile kadınlar için özel tasarlanmış t-shirt. Zarif görünüm ve rahat kullanım bir arada.',
    price: 89,
    images: [
      'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500',
      'https://images.unsplash.com/photo-1571945153237-4929e783af4a?w=500'
    ],
    category: 'women',
    sizes: ['XS', 'S', 'M', 'L'],
    colors: ['navy', 'white', 'pink'],
    stock: new Map([
      ['XS-navy', 15],
      ['S-navy', 20],
      ['M-navy', 25],
      ['L-navy', 20],
      ['XS-white', 15],
      ['S-white', 20],
      ['M-white', 25],
      ['L-white', 20],
      ['XS-pink', 15],
      ['S-pink', 20],
      ['M-pink', 25],
      ['L-pink', 20]
    ]),
    featured: false,
    tags: ['kadın', 'v-yaka', 'şık']
  }
];

export async function POST() {
  try {
    await connectDB();

    // Clear existing products
    await Product.deleteMany({});

    // Insert sample products
    const products = await Product.insertMany(sampleProducts);

    return NextResponse.json(
      { 
        message: 'Veritabanı başarıyla dolduruldu',
        count: products.length,
        products: products.map(p => ({ id: p._id, name: p.name }))
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Seed error:', error);
    return NextResponse.json(
      { error: 'Veritabanı doldurulurken hata oluştu' },
      { status: 500 }
    );
  }
}
