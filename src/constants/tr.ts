// Turkish Language Constants for ThreadCraft E-commerce

export const TR = {
  // Navigation
  nav: {
    home: 'Ana Sayfa',
    products: 'Ür<PERSON>nler',
    categories: 'Kategoriler',
    cart: 'Sepet',
    login: '<PERSON><PERSON><PERSON>',
    register: '<PERSON><PERSON><PERSON>l',
    logout: '<PERSON><PERSON><PERSON><PERSON><PERSON> Yap',
    profile: 'Profil',
    admin: '<PERSON><PERSON><PERSON><PERSON>',
    search: 'Ara...',
  },

  // Home Page
  home: {
    hero: {
      title: 'ThreadCraft ile Tarzını Yarat',
      subtitle: 'Kaliteli ve şık t-shirt koleksiyonumuzla stilini tamamla',
      cta: 'Alışverişe Başla',
    },
    featured: {
      title: 'Öne Çıkan Ürünler',
      subtitle: 'En popüler t-shirt modellerimizi keşfet',
    },
    about: {
      title: 'ThreadCraft Hakkında',
      description: 'Kaliteli kumaş ve özgün tasarımlarla üretilen t-shirt\'lerimizle tarzınızı yansıtın.',
    },
  },

  // Products
  products: {
    title: 'Ürünler',
    filters: {
      title: 'Filtrel<PERSON>',
      price: 'Fiyat',
      size: 'Beden',
      color: 'Renk',
      category: 'Kategori',
      sort: 'Sırala',
      clear: 'Temizle',
    },
    sort: {
      newest: 'En Yeni',
      oldest: 'En Eski',
      priceAsc: 'Fiyat (Düşük-Yüksek)',
      priceDesc: 'Fiyat (Yüksek-Düşük)',
      popular: 'En Popüler',
    },
    noResults: 'Ürün bulunamadı',
    loadMore: 'Daha Fazla Yükle',
  },

  // Product Detail
  product: {
    addToCart: 'Sepete Ekle',
    buyNow: 'Hemen Al',
    selectSize: 'Beden Seç',
    selectColor: 'Renk Seç',
    quantity: 'Adet',
    inStock: 'Stokta',
    outOfStock: 'Stokta Yok',
    description: 'Açıklama',
    features: 'Özellikler',
    reviews: 'Yorumlar',
    relatedProducts: 'Benzer Ürünler',
  },

  // Cart
  cart: {
    title: 'Sepetim',
    empty: 'Sepetiniz boş',
    emptyDescription: 'Alışverişe başlamak için ürünleri sepete ekleyin',
    continueShopping: 'Alışverişe Devam Et',
    remove: 'Kaldır',
    update: 'Güncelle',
    subtotal: 'Ara Toplam',
    shipping: 'Kargo',
    tax: 'KDV',
    total: 'Toplam',
    checkout: 'Ödemeye Geç',
    freeShipping: 'Ücretsiz Kargo',
  },

  // Authentication
  auth: {
    login: {
      title: 'Giriş Yap',
      email: 'E-posta',
      password: 'Şifre',
      submit: 'Giriş Yap',
      forgotPassword: 'Şifremi Unuttum',
      noAccount: 'Hesabınız yok mu?',
      createAccount: 'Hesap Oluştur',
    },
    register: {
      title: 'Kayıt Ol',
      firstName: 'Ad',
      lastName: 'Soyad',
      email: 'E-posta',
      password: 'Şifre',
      confirmPassword: 'Şifre Tekrar',
      submit: 'Kayıt Ol',
      hasAccount: 'Zaten hesabınız var mı?',
      signIn: 'Giriş Yap',
    },
    errors: {
      invalidCredentials: 'Geçersiz e-posta veya şifre',
      emailExists: 'Bu e-posta adresi zaten kullanılıyor',
      passwordMismatch: 'Şifreler eşleşmiyor',
      required: 'Bu alan zorunludur',
      invalidEmail: 'Geçerli bir e-posta adresi girin',
      passwordTooShort: 'Şifre en az 6 karakter olmalıdır',
    },
  },

  // Checkout
  checkout: {
    title: 'Ödeme',
    steps: {
      shipping: 'Teslimat Bilgileri',
      payment: 'Ödeme Bilgileri',
      review: 'Sipariş Özeti',
    },
    shipping: {
      title: 'Teslimat Adresi',
      firstName: 'Ad',
      lastName: 'Soyad',
      address: 'Adres',
      city: 'Şehir',
      postalCode: 'Posta Kodu',
      phone: 'Telefon',
    },
    payment: {
      title: 'Ödeme Yöntemi',
      creditCard: 'Kredi Kartı',
      paypal: 'PayPal',
      bankTransfer: 'Havale/EFT',
    },
    review: {
      title: 'Siparişinizi Gözden Geçirin',
      placeOrder: 'Siparişi Tamamla',
    },
  },

  // Order Confirmation
  orderConfirmation: {
    title: 'Sipariş Onayı',
    success: 'Siparişiniz başarıyla alındı!',
    orderNumber: 'Sipariş Numarası',
    thankYou: 'Teşekkür ederiz!',
    emailSent: 'Sipariş detayları e-posta adresinize gönderildi.',
    continueShopping: 'Alışverişe Devam Et',
  },

  // Common
  common: {
    loading: 'Yükleniyor...',
    error: 'Bir hata oluştu',
    retry: 'Tekrar Dene',
    save: 'Kaydet',
    cancel: 'İptal',
    delete: 'Sil',
    edit: 'Düzenle',
    view: 'Görüntüle',
    close: 'Kapat',
    next: 'İleri',
    previous: 'Geri',
    submit: 'Gönder',
    reset: 'Sıfırla',
    currency: '₺',
  },

  // Footer
  footer: {
    company: {
      title: 'ThreadCraft',
      description: 'Kaliteli ve şık t-shirt koleksiyonları',
    },
    links: {
      about: 'Hakkımızda',
      contact: 'İletişim',
      privacy: 'Gizlilik Politikası',
      terms: 'Kullanım Şartları',
      faq: 'Sık Sorulan Sorular',
      shipping: 'Kargo Bilgileri',
      returns: 'İade ve Değişim',
    },
    social: {
      title: 'Bizi Takip Edin',
      facebook: 'Facebook',
      instagram: 'Instagram',
      twitter: 'Twitter',
      youtube: 'YouTube',
    },
    newsletter: {
      title: 'Bülten',
      description: 'Yeni ürünler ve kampanyalardan haberdar olun',
      placeholder: 'E-posta adresiniz',
      subscribe: 'Abone Ol',
    },
    copyright: '© 2024 ThreadCraft. Tüm hakları saklıdır.',
  },

  // Sizes
  sizes: {
    XS: 'XS',
    S: 'S',
    M: 'M',
    L: 'L',
    XL: 'XL',
    XXL: 'XXL',
  },

  // Colors
  colors: {
    black: 'Siyah',
    white: 'Beyaz',
    red: 'Kırmızı',
    blue: 'Mavi',
    green: 'Yeşil',
    yellow: 'Sarı',
    purple: 'Mor',
    pink: 'Pembe',
    gray: 'Gri',
    navy: 'Lacivert',
  },

  // Categories
  categories: {
    men: 'Erkek',
    women: 'Kadın',
    unisex: 'Unisex',
    kids: 'Çocuk',
    casual: 'Günlük',
    sport: 'Spor',
    vintage: 'Vintage',
    graphic: 'Baskılı',
  },
};

export default TR;
